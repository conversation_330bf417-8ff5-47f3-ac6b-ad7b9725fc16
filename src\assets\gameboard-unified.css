/* Enhanced GameBoard Styling */
.gameboard-container {
  background: linear-gradient(135deg, #0277BD 0%, #01579B 50%, #004D7A 100%);
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 24px rgba(1, 87, 155, 0.3);
  margin: 16px;
  border: 2px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.gameboard-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 32px rgba(1, 87, 155, 0.4);
}

.gameboard-title {
  color: white;
  font-size: 1.5rem;
  font-weight: 600;
  text-align: center;
  margin-bottom: 20px;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.board-wrapper {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.game-board {
  display: grid;
  grid-template-columns: repeat(10, 1fr);
  gap: 2px;
  background: rgba(255, 255, 255, 0.1);
  padding: 12px;
  border-radius: 8px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
}

.board-cell {
  width: 32px;
  height: 32px;
  border: 1px solid rgba(255, 255, 255, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.05);
  position: relative;
  overflow: hidden;
}

.board-cell::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s ease;
}

.board-cell:hover::before {
  left: 100%;
}

.board-cell:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.6);
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.board-cell.water {
  background: rgba(3, 169, 244, 0.3);
  border-color: #03A9F4;
}

.board-cell.ship {
  background: linear-gradient(45deg, #37474F, #546E7A);
  border-color: #78909C;
  color: white;
  font-weight: bold;
}

.board-cell.hit {
  background: linear-gradient(45deg, #D32F2F, #F44336);
  border-color: #FF5722;
  color: white;
  animation: hitPulse 0.6s ease-out;
}

.board-cell.miss {
  background: rgba(96, 125, 139, 0.5);
  border-color: #607D8B;
  color: #B0BEC5;
}

.board-cell.sunk {
  background: linear-gradient(45deg, #424242, #616161);
  border-color: #757575;
  color: #BDBDBD;
  animation: sunkFade 1s ease-out;
}

@keyframes hitPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.2); box-shadow: 0 0 20px #F44336; }
  100% { transform: scale(1); }
}

@keyframes sunkFade {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

/* Coordinate labels */
.coordinate-row, .coordinate-col {
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 600;
  font-size: 12px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.board-with-coordinates {
  display: grid;
  grid-template-columns: 24px repeat(10, 32px);
  grid-template-rows: 24px repeat(10, 32px);
  gap: 2px;
  background: rgba(255, 255, 255, 0.05);
  padding: 16px;
  border-radius: 12px;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

/* Game status indicators */
.game-status {
  text-align: center;
  margin: 16px 0;
  padding: 12px;
  border-radius: 8px;
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-active {
  color: #4CAF50;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.status-waiting {
  color: #FF9800;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.status-hit {
  color: #F44336;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* Ship placement mode */
.placement-mode .board-cell {
  cursor: grab;
}

.placement-mode .board-cell:hover {
  background: rgba(76, 175, 80, 0.3);
  border-color: #4CAF50;
}

.placement-mode .board-cell.valid-placement {
  background: rgba(76, 175, 80, 0.5);
  border-color: #4CAF50;
  animation: placementPulse 1s ease-in-out infinite;
}

.placement-mode .board-cell.invalid-placement {
  background: rgba(244, 67, 54, 0.3);
  border-color: #F44336;
}

@keyframes placementPulse {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 0.8; }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .gameboard-container {
    margin: 8px;
    padding: 16px;
  }
  
  .board-cell {
    width: 28px;
    height: 28px;
    font-size: 12px;
  }
  
  .board-with-coordinates {
    grid-template-columns: 20px repeat(10, 28px);
    grid-template-rows: 20px repeat(10, 28px);
    padding: 12px;
  }
  
  .gameboard-title {
    font-size: 1.3rem;
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .board-cell {
    width: 24px;
    height: 24px;
    font-size: 10px;
  }
  
  .board-with-coordinates {
    grid-template-columns: 18px repeat(10, 24px);
    grid-template-rows: 18px repeat(10, 24px);
    padding: 8px;
  }
  
  .gameboard-container {
    margin: 4px;
    padding: 12px;
  }
}
