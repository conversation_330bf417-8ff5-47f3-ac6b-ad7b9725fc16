{"name": "battleship-game", "version": "1.0.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint", "dev": "vue-cli-service serve"}, "dependencies": {"@mdi/font": "^7.4.47", "vue": "^3.3.4", "vuetify": "^3.9.0-beta.1"}, "devDependencies": {"@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "autoprefixer": "^10.4.21", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "postcss": "^8.5.6", "tailwindcss": "^3.4.17"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/vue3-essential", "eslint:recommended"], "parserOptions": {"ecmaVersion": 2020}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}